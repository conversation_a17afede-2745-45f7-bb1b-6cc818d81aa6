from typing import Optional

from fastapi import APIRouter, Depends, HTTPException

from app.core.auth_guard import role_required
from app.schemas import communication as communication_schemas
from app.services.communication_service import CommunicationServiceClient
from app.shared.constants import ChatType
from app.utils.agent_utils import (
    enrich_conversations_with_agent_details,
    enrich_tasks_with_agent_details,
)
from app.utils.redis.redis_service import RedisService

# Create router
communication_router = APIRouter(prefix="/communication", tags=["communication"])
communication_service = CommunicationServiceClient()

# Create Redis service for session management
redis_service = RedisService()


# Routes for conversation management
@communication_router.post("/conversation", status_code=201)
async def create_conversation(
    conversation: communication_schemas.ConversationCreate,
    current_user: dict = Depends(role_required(["user"])),
) -> communication_schemas.ConversationResponse:
    try:
        response = await communication_service.create_conversation(
            userId=current_user["user_id"],
            chatType=conversation.chatType,
            agentId=conversation.agentId,
        )
        return communication_schemas.ConversationResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.get(
    "/conversation/{conversation_id}",
    response_model=communication_schemas.ConversationResponse,
)
async def get_conversation(
    conversation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.get_conversation(
            conversationId=conversation_id, userId=current_user["user_id"]
        )

        # Enrich with agent details if agentId is present
        if response.get("agentId"):
            enriched_conversations = await enrich_conversations_with_agent_details([response])
            if enriched_conversations:
                response = enriched_conversations[0]

        return communication_schemas.ConversationResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.delete("/conversation/{conversation_id}", status_code=204)
async def delete_conversation(
    conversation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        await communication_service.delete_conversation(
            conversationId=conversation_id, userId=current_user["user_id"]
        )
        # Return 204 No Content on successful deletion
        return None
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.get("/conversations", response_model=communication_schemas.ConversationList)
async def get_conversations(
    chatType: Optional[ChatType] = None,
    agentId: Optional[str] = None,
    search: Optional[str] = None,
    page: Optional[int] = 1,
    limit: Optional[int] = 10,
    # current_user: dict = Depends(role_required(["user"])),
):
    """
    List conversations with enhanced filtering options.

    Supports 5 filtering modes:
    1. All conversations (default when no chatType provided) - returns all user conversations ordered by latest first
    2. All conversations of specific type - filters by chatType (CHAT_TYPE_GLOBAL, CHAT_TYPE_AGENT)
    3. By agentId - filters by specific agent conversations
    4. By search query - searches in conversation titles using regex
    5. Combined filters - any combination of the above filters

    Args:
        chatType: Optional chat type to filter conversations by (if not provided, returns all conversations)
        agentId: Optional agent ID to filter conversations by specific agent
        search: Optional search query to search in conversation titles
        page: Page number for pagination (default: 1)
        limit: Number of items per page (default: 10)
        current_user: Current authenticated user

    Returns:
        ConversationList: List of conversations matching the filters with pagination metadata
    """
    try:
        response = await communication_service.list_conversations(
            userId="current_user["user_id"]",
            chatType=chatType,
            page=page,
            limit=limit,
            agentId=agentId,
            search=search,
        )

        # Ensure response has the expected structure
        if not response:
            response = {"data": [], "metadata": {}}

        # Ensure data key exists and is a list
        if "data" not in response or not isinstance(response["data"], list):
            response["data"] = []

        # Ensure metadata key exists
        if "metadata" not in response:
            response["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": page,
                "pageSize": limit,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }

        # If data is empty, return empty list with metadata
        if len(response["data"]) == 0:
            return communication_schemas.ConversationList(
                data=[],
                metadata=response["metadata"],
            )

        # Enrich conversations with agent details
        enriched_conversations = await enrich_conversations_with_agent_details(response["data"])

        # Process and return the conversation list
        return communication_schemas.ConversationList(
            data=[
                communication_schemas.ConversationResponse(**conv)
                for conv in enriched_conversations
            ],
            metadata=response["metadata"],
        )
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.patch("/conversation/{conversation_id}/tokens", status_code=204)
async def update_conversation_tokens(
    conversation_id: str,
    tokens_update: communication_schemas.UpdateConversationTokensRequest,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        await communication_service.update_conversation_tokens(
            conversationId=conversation_id,
            userId=current_user["user_id"],
            inputTokens=tokens_update.inputTokens,
            outputTokens=tokens_update.outputTokens,
        )
        # Return 204 No Content on successful update
        return None
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


# Routes for message management
@communication_router.post(
    "/message", response_model=communication_schemas.MessageResponse, status_code=201
)
async def add_message(
    message: communication_schemas.MessageCreate,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.create_message(
            conversationId=message.conversationId,
            senderType=message.senderType,
            userId=current_user["user_id"],
            data=message.data,
            workflowId=message.workflowId,
            workflowResponse=message.workflowResponse,
            status=message.status,
            type=message.type,
        )
        return communication_schemas.MessageResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.post(
    "/direct-workflow-execution",
    response_model=communication_schemas.MessageResponse,
    status_code=201,
)
async def direct_workflow_execution(
    request: communication_schemas.DirectWorkflowExecutionRequest,
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Execute a workflow directly and save the message with predefined settings.

    This endpoint creates a message with:
    - Sender type: USER
    - Message type: USER_MESSAGE
    - Status: COMPLETED

    Args:
        request: Direct workflow execution request containing session_id, workflow_id,
                correlation_id, message, and conversation_id
        current_user: Current authenticated user

    Returns:
        MessageResponse: The created message response
    """
    try:
        # Create a MessageCreate object with predefined values
        message_create = communication_schemas.MessageCreate(
            conversationId=request.conversationId,
            senderType=communication_schemas.SenderType.SENDER_TYPE_USER,
            data={
                "message": request.message,
                "sessionId": request.sessionId,
                "correlationId": request.correlationId,
            },
            workflowId=request.workflowId,
            workflowResponse=None,
            status=communication_schemas.MessageStatus.MESSAGE_STATUS_COMPLETED,
            type=communication_schemas.MessageType.MESSAGE_TYPE_USER_MESSAGE,
        )

        # Store session-conversation mapping in Redis for quick lookup
        session_key = f"session:{request.sessionId}"
        session_data = {
            "conversation_id": request.conversationId,
            "user_id": current_user["user_id"],
            "workflow_id": request.workflowId,
            "correlation_id": request.correlationId,
            # "agent_id": request.agent_id,
            # "session_type": session_type,
            # "organization_id": organization_id,
        }

        redis_service.set_data_to_redis(session_key, "data", session_data)

        # Store session data in Redis
        session_key = f"session_id:{request.correlationId}"
        session_data = {"workflow_id": request.workflowId, "session_id": request.sessionId}

        redis_service.set_data_to_redis(
            session_key,
            "session",
            session_data,
        )

        # Call the existing add_message logic internally
        response = await communication_service.create_message(
            conversationId=message_create.conversationId,
            senderType=message_create.senderType,
            userId=current_user["user_id"],
            data=message_create.data,
            workflowId=message_create.workflowId,
            workflowResponse=message_create.workflowResponse,
            status=message_create.status,
            type=message_create.type,
        )
        return communication_schemas.MessageResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.delete("/message/{message_id}", status_code=204)
async def delete_message(
    message_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        await communication_service.delete_message(
            messageId=message_id, userId=current_user["user_id"]
        )
        # Return 204 No Content on successful deletion
        return None
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.get(
    "/messages/{conversation_id}", response_model=communication_schemas.MessageList
)
async def get_messages(
    conversation_id: str,
    page: Optional[int] = 1,
    limit: Optional[int] = 10,
    index: Optional[int] = None,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.list_messages(
            conversationId=conversation_id,
            userId=current_user["user_id"],
            page=page,
            limit=limit,
            index=index,
        )

        # Ensure response has the expected structure
        if not response:
            response = {"data": [], "metadata": {}}

        # Ensure data key exists and is a list
        if "data" not in response or not isinstance(response["data"], list):
            response["data"] = []

        # Ensure metadata key exists
        if "metadata" not in response:
            response["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": page,
                "pageSize": limit,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }

        # If data is empty, return empty list with metadata
        if len(response["data"]) == 0:
            return communication_schemas.MessageList(
                data=[],
                metadata=response["metadata"],
            )

        # Process and return the message list
        return communication_schemas.MessageList(
            data=[communication_schemas.MessageResponse(**msg) for msg in response["data"]],
            metadata=response["metadata"],
        )
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


# Routes for task management
@communication_router.post(
    "/task", response_model=communication_schemas.TaskResponse, status_code=201
)
async def create_task(
    task: communication_schemas.TaskCreate,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.create_task(
            globalChatConversationId=task.globalChatConversationId,
            title=task.title,
            agentConversationId=task.agentConversationId,
            agentId=task.agentId,
            userId=current_user["user_id"],
            taskStatus=task.taskStatus,
            correlationId=task.correlationId,
            sessionId=task.sessionId,
        )
        return communication_schemas.TaskResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.delete("/task/{task_id}", status_code=204)
async def delete_task(
    task_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        await communication_service.delete_task(taskId=task_id, userId=current_user["user_id"])
        # Return 204 No Content on successful deletion
        return None
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.get("/tasks", response_model=communication_schemas.TaskList)
async def get_tasks(
    globalChatConversationId: Optional[str] = None,
    agentId: Optional[str] = None,
    search: Optional[str] = None,
    taskStatus: Optional[communication_schemas.TaskStatus] = None,
    page: Optional[int] = 1,
    limit: Optional[int] = 10,
    current_user: dict = Depends(role_required(["user"])),
):
    """
    List tasks with enhanced filtering options.

    Supports 5 filtering modes:
    1. All tasks (if no globalChatConversationId) - returns all user's tasks ordered by latest first
    2. By globalChatConversationId - filters tasks for specific conversation
    3. By agentId - filters tasks by specific agent
    4. By search query - searches in task titles using regex
    5. By taskStatus - filters tasks by specific status

    Args:
        globalChatConversationId: Optional conversation ID to filter tasks for specific conversation
        agentId: Optional agent ID to filter tasks by specific agent
        search: Optional search query to search in task titles
        taskStatus: Optional task status to filter by specific status
        page: Page number for pagination (default: 1)
        limit: Number of items per page (default: 10)
        current_user: Current authenticated user

    Returns:
        TaskList: List of tasks matching the filters with pagination metadata
    """
    try:
        response = await communication_service.list_tasks(
            userId=current_user["user_id"],
            globalChatConversationId=globalChatConversationId,
            page=page,
            limit=limit,
            agentId=agentId,
            search=search,
            taskStatus=taskStatus,
        )

        # Ensure response has the expected structure
        if not response:
            response = {"data": [], "metadata": {}}

        # Ensure data key exists and is a list
        if "data" not in response or not isinstance(response["data"], list):
            response["data"] = []

        # Ensure metadata key exists
        if "metadata" not in response:
            response["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": page,
                "pageSize": limit,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }

        # If data is empty, return empty list with metadata
        if len(response["data"]) == 0:
            return communication_schemas.TaskList(
                data=[],
                metadata=response["metadata"],
            )

        # Enrich tasks with agent details
        enriched_tasks = await enrich_tasks_with_agent_details(response["data"])

        # Process and return the task list
        return communication_schemas.TaskList(
            data=[communication_schemas.TaskResponse(**task) for task in enriched_tasks],
            metadata=response["metadata"],
        )
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.post(
    "/task/delegate", response_model=communication_schemas.TaskDelegationResponse, status_code=201
)
async def delegate_task(
    task_delegation: communication_schemas.TaskDelegationRequest,
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Delegate a task to an agent by creating a new agent conversation, session, and task.

    This endpoint performs five main operations in sequence:
    1. Updates the agent with workflow and MCP tool IDs if provided
    2. Creates a new conversation for the specified agent
    3. Creates a session using the new conversation
    4. Sends an initial message to the agent via quick chat
    5. Creates a task linking the global chat conversation to the agent conversation

    Args:
        task_delegation: Task delegation request containing:
            - title: Task title/description
            - globalChatConversationId: ID of the global chat conversation
            - agentId: ID of the agent to delegate to
            - correlationId: Optional correlation ID for tracking
            - workflowId: Optional list of workflow IDs to update agent with
            - mcpToolId: Optional list of MCP tool IDs to update agent with
        current_user: Current authenticated user

    Returns:
        TaskDelegationResponse: Contains success status, message, and session ID for further interaction

    Raises:
        HTTPException: If any step of the task delegation process fails
    """
    try:
        response = await communication_service.task_delegation(
            title=task_delegation.title,
            globalChatConversationId=task_delegation.globalChatConversationId,
            agentId=task_delegation.agentId,
            userId=current_user["user_id"],
            correlationId=task_delegation.correlationId,
            workflowId=task_delegation.workflowId,
            mcpToolId=task_delegation.mcpToolId,
            globalSessionId=task_delegation.globalSessionId,
            organization_id=current_user.get("organization_id"),
        )
        return communication_schemas.TaskDelegationResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.patch("/task/{task_id}/status", status_code=204)
async def update_task_status(
    task_id: str,
    status_update: communication_schemas.UpdateTaskStatusRequest,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        await communication_service.update_task_status(
            taskId=task_id,
            taskStatus=status_update.taskStatus,
            userId=current_user["user_id"],
        )
        # Return 204 No Content on successful update
        return None
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code
