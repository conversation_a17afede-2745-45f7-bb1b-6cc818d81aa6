import asyncio
import queue
import uuid
from typing import List, Optional

import grpc
from fastapi import HTTPException
from google.protobuf import struct_pb2, wrappers_pb2
from google.protobuf.json_format import MessageToDict

from app.core.config import settings
from app.core.logging import get_logger
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.helper.sse_manager import sse_manager
from app.schemas.communication import MessageList, MessageResponse
from app.shared.constants import (
    CHAT_TYPE_TO_INT,
    INT_TO_CHAT_TYPE,
    INT_TO_MESSAGE_STATUS,
    INT_TO_MESSAGE_TYPE,
    INT_TO_SENDER_TYPE,
    INT_TO_TASK_STATUS,
    MESSAGE_STATUS_TO_INT,
    MESSAGE_TYPE_TO_INT,
    SENDER_TYPE_TO_INT,
    TASK_STATUS_TO_INT,
    ChatType,
    MessageStatus,
    MessageType,
    SenderType,
    TaskStatus,
)
from app.utils.agent_utils import fetch_agents_details
from app.utils.redis.redis_service import RedisService

# Reference wrapper types to ensure registration for Any unpacking
_ = (
    wrappers_pb2.StringValue,
    wrappers_pb2.Int64Value,
    wrappers_pb2.DoubleValue,
    wrappers_pb2.BoolValue,
    struct_pb2.Struct,
    struct_pb2.ListValue,
)

# Initialize logger
logger = get_logger(__name__)


class CommunicationServiceClient:
    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.COMMUNICATION_SERVICE_HOST}:{settings.COMMUNICATION_SERVICE_PORT}"
        )
        self.stub = communication_pb2_grpc.CommunicationServiceStub(self.channel)
        self.redis_service = RedisService()

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        else:
            raise HTTPException(status_code=500, detail=details)

    def _convert_grpc_response_enums(self, response_dict):
        """Convert integer enum values in the gRPC response to string enum values."""
        if "chatType" in response_dict and isinstance(response_dict["chatType"], int):
            response_dict["chatType"] = INT_TO_CHAT_TYPE.get(
                response_dict["chatType"], ChatType.CHAT_TYPE_UNSPECIFIED
            )

        if "senderType" in response_dict and isinstance(response_dict["senderType"], int):
            response_dict["senderType"] = INT_TO_SENDER_TYPE.get(
                response_dict["senderType"], SenderType.SENDER_TYPE_UNSPECIFIED
            )

        if "taskStatus" in response_dict and isinstance(response_dict["taskStatus"], int):
            response_dict["taskStatus"] = INT_TO_TASK_STATUS.get(
                response_dict["taskStatus"], TaskStatus.TASK_STATUS_UNSPECIFIED
            )

        # Handle new message status field
        if "status" in response_dict and isinstance(response_dict["status"], int):
            response_dict["status"] = INT_TO_MESSAGE_STATUS.get(
                response_dict["status"], MessageStatus.MESSAGE_STATUS_UNSPECIFIED
            )

        # Handle new message type field
        if "type" in response_dict and isinstance(response_dict["type"], int):
            response_dict["type"] = INT_TO_MESSAGE_TYPE.get(
                response_dict["type"], MessageType.MESSAGE_TYPE_UNSPECIFIED
            )

        # Handle tasks field in conversations (repeated Task field)
        if "tasks" in response_dict and isinstance(response_dict["tasks"], list):
            for task in response_dict["tasks"]:
                if isinstance(task, dict):
                    self._convert_grpc_response_enums(task)

        return response_dict

    def _process_conversations_list(self, response_dict):
        """Process conversation list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for conv in response_dict["data"]:
                self._convert_grpc_response_enums(conv)
                # Ensure tasks field is properly initialized if not present
                if "tasks" not in conv:
                    conv["tasks"] = []

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    def _process_messages_list(self, response_dict):
        """Process message list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for msg in response_dict["data"]:
                self._convert_grpc_response_enums(msg)

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    def _process_tasks_list(self, response_dict):
        """Process task list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for task in response_dict["data"]:
                self._convert_grpc_response_enums(task)

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    def _process_task_response(self, response_dict):
        """Process single task response to convert enum values and ensure required fields."""
        # Convert enum values
        self._convert_grpc_response_enums(response_dict)

        # Ensure taskStatus is present for task responses
        if "taskStatus" not in response_dict:
            logger.warning("taskStatus field missing from task response, setting default")
            response_dict["taskStatus"] = TaskStatus.TASK_STATUS_UNSPECIFIED

        return response_dict

    def _dict_to_struct(self, data_dict):
        """Convert a Python dictionary to a protobuf Struct."""
        if data_dict is None:
            return None

        struct = struct_pb2.Struct()
        struct.update(data_dict)
        return struct

    async def create_conversation(
        self,
        userId: str,
        chatType: ChatType,
        agentId: Optional[str] = None,
    ):
        # Convert string enum to integer for gRPC
        chat_type_int = CHAT_TYPE_TO_INT.get(chatType, 0)

        request = communication_pb2.CreateConversationRequest(
            userId=userId,
            agentId=agentId,
            chatType=chat_type_int,
        )

        try:
            response = self.stub.createConversation(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            logger.debug("Create conversation response", extra={"response": response_dict})
            # Convert integer enums to string enums and ensure tasks field
            result = self._convert_grpc_response_enums(response_dict)
            # Ensure tasks field is properly initialized if not present
            if "tasks" not in result:
                result["tasks"] = []
            return result
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_conversation(self, conversationId: str, userId: str):
        request = communication_pb2.GetConversationRequest(
            conversationId=conversationId, userId=userId
        )

        try:
            response = self.stub.getConversation(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Convert integer enums to string enums and ensure tasks field
            result = self._convert_grpc_response_enums(response_dict)
            # Ensure tasks field is properly initialized if not present
            if "tasks" not in result:
                result["tasks"] = []
            return result
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_conversation(self, conversationId: str, userId: str):
        request = communication_pb2.DeleteConversationRequest(
            conversationId=conversationId, userId=userId
        )

        try:
            response = self.stub.deleteConversation(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_conversations(
        self,
        userId: str,
        chatType: Optional[ChatType] = None,
        page: int = 1,
        limit: int = 10,
        agentId: Optional[str] = None,
        search: Optional[str] = None,
    ):
        # Convert string enum to integer for gRPC
        # If chatType is None, use CHAT_TYPE_UNSPECIFIED (0) to get all conversations
        chat_type_int = CHAT_TYPE_TO_INT.get(chatType, 0) if chatType is not None else 0

        request = communication_pb2.ListConversationsRequest(
            userId=userId,
            chatType=chat_type_int,
            page=page,
            limit=limit,
            agentId=agentId,
            search=search,
        )

        try:
            response = self.stub.listConversations(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            logger.debug("Raw listConversations response", extra={"response": response_dict})
            # Process conversation list to convert enum values
            processed_response = self._process_conversations_list(response_dict)
            logger.debug(
                "Processed listConversations response", extra={"response": processed_response}
            )
            return processed_response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_conversation_tokens(
        self,
        conversationId: str,
        userId: str,
        inputTokens: Optional[int] = None,
        outputTokens: Optional[int] = None,
    ):
        request = communication_pb2.UpdateConversationTokensRequest(
            conversationId=conversationId,
            userId=userId,
            inputTokens=inputTokens,
            outputTokens=outputTokens,
        )

        try:
            response = self.stub.updateConversationTokens(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_message(
        self,
        conversationId: str,
        senderType: SenderType,
        userId: str,
        data: Optional[dict] = None,
        workflowId: Optional[str] = None,
        workflowResponse: Optional[List[dict]] = None,
        status: Optional[MessageStatus] = None,
        type: MessageType = MessageType.MESSAGE_TYPE_CHAT,
    ):
        # Convert string enums to integers for gRPC
        sender_type_int = SENDER_TYPE_TO_INT.get(senderType, 0)
        message_status_int = MESSAGE_STATUS_TO_INT.get(status, 0) if status else 0
        message_type_int = MESSAGE_TYPE_TO_INT.get(type, 0)

        # Convert data dict to protobuf Struct if provided
        data_struct = self._dict_to_struct(data) if data is not None else None

        # Convert workflowResponse list of dicts to list of protobuf Structs
        workflow_response_structs = []
        if workflowResponse:
            for response_dict in workflowResponse:
                if response_dict:  # Only convert non-empty dictionaries
                    workflow_response_structs.append(self._dict_to_struct(response_dict))

        request = communication_pb2.CreateMessageRequest(
            conversationId=conversationId,
            senderType=sender_type_int,
            data=data_struct,
            workflowId=workflowId,
            workflowResponse=workflow_response_structs,
            userId=userId,
            status=message_status_int,
            type=message_type_int,
        )

        try:
            response = self.stub.createMessage(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Convert integer enums to string enums
            return self._convert_grpc_response_enums(response_dict)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_message(self, messageId: str, userId: str):
        request = communication_pb2.DeleteMessageRequest(messageId=messageId, userId=userId)

        try:
            response = self.stub.deleteMessage(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_messages(
        self,
        conversationId: str,
        userId: str,
        page: int = 1,
        limit: int = 10,
        index: Optional[int] = None,
    ):
        request = communication_pb2.ListMessagesRequest(
            conversationId=conversationId, page=page, limit=limit, userId=userId, index=index
        )

        try:
            response = self.stub.listMessages(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            logger.debug("Raw listMessages response", extra={"response": response_dict})
            # Process messages list to convert enum values
            processed_response = self._process_messages_list(response_dict)
            logger.debug("Processed listMessages response", extra={"response": processed_response})
            return processed_response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_message_workflow_response(
        self,
        messageId: str,
        userId: str,
        newWorkflowResponse: dict,
    ):
        """
        Update the workflow response for a specific message.

        This is an internal function used by the service layer to update
        workflow responses for messages. It's not exposed as a public API endpoint.

        Args:
            messageId: The ID of the message to update
            userId: The ID of the user making the request
            newWorkflowResponse: The new workflow response data to add

        Returns:
            dict: Updated message response
        """
        # Convert the workflow response to protobuf Struct directly
        workflow_struct = self._dict_to_struct(newWorkflowResponse)

        request = communication_pb2.UpdateMessageWorkflowResponseRequest(
            messageId=messageId,
            userId=userId,
            newWorkflowResponse=workflow_struct,
        )

        try:
            response = self.stub.updateMessageWorkflowResponse(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            logger.debug("Update message workflow response", extra={"response": response_dict})
            # Convert integer enums to string enums
            return self._convert_grpc_response_enums(response_dict)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_message_status(
        self,
        messageId: str,
        userId: str,
        status: MessageStatus,
    ):
        """
        Update the status of a specific message.

        Args:
            messageId: The ID of the message to update
            userId: The ID of the user making the request
            status: The new status to set for the message

        Returns:
            dict: Updated message response
        """
        # Convert string enum to integer for gRPC
        message_status_int = MESSAGE_STATUS_TO_INT.get(status, 0)

        request = communication_pb2.UpdateMessageStatusRequest(
            messageId=messageId,
            userId=userId,
            status=message_status_int,
        )

        try:
            response = self.stub.updateMessageStatus(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            logger.debug("Update message status response", extra={"response": response_dict})
            # Convert integer enums to string enums
            return self._convert_grpc_response_enums(response_dict)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_task(
        self,
        globalChatConversationId: str,
        title: str,
        agentConversationId: str,
        agentId: str,
        userId: str,
        taskStatus: TaskStatus,
        correlationId: Optional[str] = None,
        sessionId: Optional[str] = None,
    ):
        # Convert string enum to integer for gRPC
        task_status_int = TASK_STATUS_TO_INT.get(taskStatus, 0)

        request = communication_pb2.CreateTaskRequest(
            globalChatConversationId=globalChatConversationId,
            title=title,
            agentConversationId=agentConversationId,
            agentId=agentId,
            correlationId=correlationId,
            taskStatus=task_status_int,
            sessionId=sessionId,
            userId=userId,
        )

        try:
            response = self.stub.createTask(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            logger.debug("Create task response", extra={"response": response_dict})
            # Process task response to convert enum values and ensure required fields
            return self._process_task_response(response_dict)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_task_status(self, taskId: str, taskStatus: TaskStatus, userId: str):
        # Convert string enum to integer for gRPC
        task_status_int = TASK_STATUS_TO_INT.get(taskStatus, 0)

        request = communication_pb2.UpdateTaskStatusRequest(
            taskId=taskId,
            taskStatus=task_status_int,
            userId=userId,
        )

        try:
            response = self.stub.updateTaskStatus(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_task(self, taskId: str, userId: str):
        request = communication_pb2.DeleteTaskRequest(taskId=taskId, userId=userId)

        try:
            response = self.stub.deleteTask(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_tasks(
        self,
        userId: str,
        globalChatConversationId: Optional[str] = None,
        page: int = 1,
        limit: int = 10,
        agentId: Optional[str] = None,
        search: Optional[str] = None,
        taskStatus: Optional[TaskStatus] = None,
    ):
        """
        List tasks with enhanced filtering options.

        Supports 5 filtering modes:
        1. All tasks (if no globalChatConversationId) - returns all user's tasks ordered by latest first
        2. By globalChatConversationId - filters tasks for specific conversation
        3. By agentId - filters tasks by specific agent
        4. By search query - searches in task titles using regex
        5. By taskStatus - filters tasks by specific status

        Args:
            userId: User ID to filter tasks for
            globalChatConversationId: Optional conversation ID to filter tasks for specific conversation
            page: Page number for pagination (default: 1)
            limit: Number of items per page (default: 10)
            agentId: Optional agent ID to filter tasks by specific agent
            search: Optional search query to search in task titles
            taskStatus: Optional task status to filter by specific status

        Returns:
            Dictionary containing filtered tasks list with pagination metadata
        """
        # Convert string enum to integer for gRPC if provided
        task_status_int = None
        if taskStatus is not None:
            task_status_int = TASK_STATUS_TO_INT.get(taskStatus, 0)

        # Build request with only non-empty/non-None values
        request_kwargs = {
            "page": page,
            "limit": limit,
            "userId": userId,
            "globalChatConversationId": globalChatConversationId
            or "",  # Empty string for all tasks
        }

        # Only add optional fields if they have meaningful values
        if agentId:
            request_kwargs["agentId"] = agentId
        if search:
            request_kwargs["search"] = search
        if task_status_int is not None and task_status_int != 0:
            request_kwargs["taskStatus"] = task_status_int

        request = communication_pb2.ListTasksRequest(**request_kwargs)

        try:
            response = self.stub.listTasks(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            logger.debug("Raw listTasks response", extra={"response": response_dict})
            # Process tasks list to convert enum values
            processed_response = self._process_tasks_list(response_dict)
            logger.debug("Processed listTasks response", extra={"response": processed_response})
            return processed_response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def task_delegation(
        self,
        title: str,
        globalChatConversationId: str,
        agentId: str,
        userId: str,
        correlationId: Optional[str] = None,
        workflowId: Optional[List[str]] = None,
        mcpToolId: Optional[List[str]] = None,
        globalSessionId: Optional[str] = None,
        organization_id: Optional[str] = None,
    ):
        """
        Delegate a task to an agent by creating a new agent conversation and task.

        This method immediately returns a response to the frontend and then performs
        three main operations asynchronously in the background:
        1. Creates a new conversation for the specified agent
        2. Sends the task to Kafka service for execution (which creates a session and returns session_id)
        3. Creates a task linking the global chat conversation to the agent conversation using the session_id

        Args:
            title: The title/description of the task
            globalChatConversationId: ID of the global chat conversation
            agentId: ID of the agent to delegate the task to
            userId: ID of the user creating the task
            correlationId: Optional correlation ID for tracking
            workflowId: Optional list of workflow IDs to send to Kafka
            mcpToolId: Optional list of MCP tool IDs to send to Kafka

        Returns:
            dict: Immediate response with task delegation status
            {
                "success": True,
                "message": "Task delegation initiated successfully"
            }

        Note:
            All delegation steps happen asynchronously in the background.
            Any errors during background processing are logged but don't affect the immediate response.
        """

        # Generate a correlation ID if not provided for tracking
        if not correlationId:
            correlationId = str(uuid.uuid4())

        logger.info(
            "Task delegation initiated - returning immediate response",
            extra={
                "title": title,
                "globalChatConversationId": globalChatConversationId,
                "agentId": agentId,
                "userId": userId,
                "correlationId": correlationId,
                "workflowId": workflowId,
                "mcpToolId": mcpToolId,
                "globalSessionId": globalSessionId,
            },
        )

        # Start background task delegation process
        asyncio.create_task(
            self._execute_task_delegation_background(
                title=title,
                globalChatConversationId=globalChatConversationId,
                agentId=agentId,
                userId=userId,
                correlationId=correlationId,
                workflowId=workflowId,
                mcpToolId=mcpToolId,
                globalSessionId=globalSessionId,
                organization_id=organization_id,
            )
        )

        # Return immediate response with correlation ID and global session ID
        response = {
            "success": True,
            "message": "Task delegation initiated successfully",
            "correlationId": correlationId,
        }

        if globalSessionId:
            response["globalSessionId"] = globalSessionId

        return response

    async def _execute_task_delegation_background(
        self,
        title: str,
        globalChatConversationId: str,
        agentId: str,
        userId: str,
        correlationId: str,
        workflowId: Optional[List[str]] = None,
        mcpToolId: Optional[List[str]] = None,
        globalSessionId: Optional[str] = None,
        organization_id: Optional[str] = None,
    ):
        """
        Execute the actual task delegation steps in the background.

        This method handles all errors gracefully and logs them without raising exceptions
        since it runs in the background.
        """
        try:
            logger.info(
                "Starting background task delegation execution",
                extra={
                    "correlationId": correlationId,
                    "title": title,
                    "agentId": agentId,
                    "userId": userId,
                },
            )

            # Step 1: Create a new conversation for the agent
            logger.info(
                "Step 1: Creating agent conversation for task delegation",
                extra={
                    "agentId": agentId,
                    "userId": userId,
                    "correlationId": correlationId,
                },
            )

            try:
                agent_conversation = await self.create_conversation(
                    userId=userId,
                    chatType=ChatType.CHAT_TYPE_AGENT,
                    agentId=agentId,
                )
                logger.info(
                    "Agent conversation created",
                    extra={
                        "conversation_response": agent_conversation,
                        "agentId": agentId,
                        "correlationId": correlationId,
                    },
                )
            except Exception as e:
                logger.error(
                    "Failed to create agent conversation in background task",
                    extra={
                        "error": str(e),
                        "agentId": agentId,
                        "userId": userId,
                        "correlationId": correlationId,
                    },
                    exc_info=True,
                )
                # Send SSE failure event
                self._send_task_delegation_failure_event(
                    error_code="CONVERSATION_CREATION_FAILED",
                    error_message=f"Failed to create agent conversation: {str(e)}",
                    correlation_id=correlationId,
                    global_session_id=globalSessionId,
                    user_id=userId,
                )
                return

            agent_conversation_id = agent_conversation.get("id")
            if not agent_conversation_id:
                logger.error(
                    "No conversation ID in agent conversation response",
                    extra={
                        "agent_conversation": agent_conversation,
                        "correlationId": correlationId,
                    },
                )
                return

            logger.info(
                "Agent conversation created successfully",
                extra={
                    "agentConversationId": agent_conversation_id,
                    "agentId": agentId,
                    "correlationId": correlationId,
                },
            )

            # Step 2: Send task to Kafka service for execution (which will create session and return session_id)
            logger.info(
                "Step 2: Sending task to Kafka service for execution",
                extra={
                    "agentId": agentId,
                    "task_query": title,
                    "workflowId": workflowId,
                    "mcpToolId": mcpToolId,
                    "userId": userId,
                    "correlationId": correlationId,
                },
            )

            # Initialize conversation context
            conversation_messages = []

            try:

                # Get conversation messages
                messages_response_raw = await self.list_messages(
                    globalChatConversationId, userId, page=1, limit=10
                )

                messages_response = MessageList(
                    data=[MessageResponse(**msg) for msg in messages_response_raw["data"]],
                    metadata=messages_response_raw["metadata"],
                )

                if messages_response.data:
                    messages_data = messages_response.data

                    # Convert messages to chat context format
                    for msg in messages_data:
                        sender_type = msg.senderType
                        # Extract content from data field (now a dictionary)
                        content = None
                        if msg.data and isinstance(msg.data, dict):
                            content = msg.data.get("message")

                        if content:  # Only include messages with content
                            role = "user" if sender_type == "SENDER_TYPE_USER" else "assistant"
                            conversation_messages.append({"role": role, "content": content})

                    logger.info(
                        "Loaded conversation messages for session creation",
                        extra={
                            "conversation_id": globalChatConversationId,
                            "message_count": len(conversation_messages),
                            "operation": "create_session",
                        },
                    )

                conversation_messages.reverse()

            except Exception as e:
                logger.error(
                    "Failed to load conversation messages",
                    extra={
                        "error": str(e),
                        "conversation_id": globalChatConversationId,
                        "user_id": userId,
                        "operation": "create_session",
                    },
                    exc_info=True,
                )

            try:

                # Import KafkaService here to avoid circular import
                from app.services.kafka_service import kafka_service

                # Prepare task data for Kafka
                task_data = {
                    "query": title,
                    "user_id": userId,
                    "conversation_id": agent_conversation_id,
                    "organization_id": organization_id,
                    "global_conversation_id": globalChatConversationId,
                }

                # Send to Kafka execute_agent_task and get session_id in response
                kafka_response = await kafka_service.execute_agent_task(
                    agent_id=agentId,
                    task=task_data,
                    workflow_ids=workflowId,
                    mcp_tool_ids=mcpToolId,
                    conversation_messages=conversation_messages,
                )

                # Extract session_id from Kafka response
                session_id = kafka_response.get("session_id")
                if not session_id:
                    logger.error(
                        "No session_id received from Kafka response",
                        extra={
                            "kafka_response": kafka_response,
                            "agentId": agentId,
                            "correlationId": correlationId,
                        },
                    )
                    return

                # Create a queue for this client to receive SSE events with enhanced size
                client_queue = queue.Queue(200)  # Increased from 100 to 200

                # Add the client to the SSE manager
                sse_manager.add_client(session_id, client_queue)

                key = f"conversation:{agent_conversation_id}"

                conversation_data = {"session_id": session_id, "session_type": "task"}

                self.redis_service.set_data_to_redis(
                    key, "conversation", conversation_data, 3600
                )  # 1 hour

                logger.info(
                    "Task sent to Kafka successfully and session_id received",
                    extra={
                        "sessionId": session_id,
                        "agentId": agentId,
                        "kafka_response": str(kafka_response)[:200],  # Truncate for logging
                        "correlationId": correlationId,
                    },
                )
            except Exception as e:
                logger.error(
                    "Failed to send task to Kafka in background task",
                    extra={
                        "error": str(e),
                        "agentId": agentId,
                        "task_query": title,
                        "userId": userId,
                        "correlationId": correlationId,
                    },
                    exc_info=True,
                )
                # Send SSE failure event
                self._send_task_delegation_failure_event(
                    error_code="KAFKA_EXECUTION_FAILED",
                    error_message=f"Failed to send task to Kafka: {str(e)}",
                    correlation_id=correlationId,
                    global_session_id=globalSessionId,
                    user_id=userId,
                )
                return

            # Step 3: Create the task linking global chat to agent conversation using session_id from Kafka
            logger.info(
                "Step 3: Creating task for delegation",
                extra={
                    "title": title,
                    "globalChatConversationId": globalChatConversationId,
                    "agentConversationId": agent_conversation_id,
                    "correlationId": correlationId,
                    "sessionId": session_id,
                },
            )

            try:
                task = await self.create_task(
                    globalChatConversationId=globalChatConversationId,
                    title=title,
                    agentConversationId=agent_conversation_id,
                    agentId=agentId,
                    userId=userId,
                    taskStatus=TaskStatus.TASK_STATUS_RUNNING,
                    correlationId=correlationId,
                    sessionId=session_id,
                )

                task_id = task.get("id")

                # Store task details in Redis using session_id as key
                if task_id and session_id:
                    # Use session_id as the Redis key instead of task_id
                    session_key = f"session:{session_id}"
                    task_data = {
                        "task_id": task_id,
                        "agent_id": agentId,
                        "conversation_id": agent_conversation_id,
                        "user_id": userId,
                        "title": title,
                        "session_id": session_id,
                        "correlation_id": correlationId,
                        "workflow_id": workflowId,
                        "mcp_tool_id": mcpToolId,
                        "session_type": "task",
                        "organization_id": organization_id,
                    }
                    self.redis_service.set_data_to_redis(
                        session_key,
                        "data",
                        task_data,
                    )  # 24 hours

                    logger.info(
                        "Task details stored in Redis using session_id as key",
                        extra={
                            "taskId": task_id,
                            "sessionId": session_id,
                            "redis_key": session_key,
                            "correlationId": correlationId,
                        },
                    )

                try:

                    agents_data = await fetch_agents_details([agentId])

                    agents_data = agents_data[agentId].dict()

                    agent_data = {
                        "id": agents_data.get("id"),
                        "name": agents_data.get("name"),
                        "description": agents_data.get("description"),
                        "avatar": agents_data.get("avatar"),
                        "organization_id": agents_data.get("organization_id"),
                        "department": agents_data.get("department"),
                    }

                    await self.create_message(
                        conversationId=globalChatConversationId,
                        senderType=SenderType.SENDER_TYPE_ASSISTANT,
                        data={
                            "message": "task delegated successfully",
                            "query": title,
                            "agent_data": agent_data,
                            "mode": "delegated",
                            "agent_conversation_id": agent_conversation_id,
                        },
                        userId=userId,
                        status=MessageStatus.MESSAGE_STATUS_COMPLETED,
                        type=MessageType.MESSAGE_TYPE_CHAT,
                    )
                except Exception as sse_error:
                    logger.error(
                        "Failed to store the message",
                        extra={
                            "error": str(sse_error),
                            "taskId": task_id,
                            "sessionId": session_id,
                            "correlationId": correlationId,
                        },
                        exc_info=True,
                    )

                # Step 4: Send SSE success event
                try:
                    client_id = globalSessionId if globalSessionId else userId

                    sse_manager.send_task_delegation_success(
                        task_id=task_id,
                        session_id=session_id,
                        agent_session_id=session_id,  # Same as session_id
                        conversation_id=agent_conversation_id,
                        agent_id=agentId,
                        global_session_id=globalSessionId,
                        correlation_id=correlationId,
                        title=title,
                        client_id=client_id,
                    )

                    logger.info(
                        "SSE task delegation success event sent",
                        extra={
                            "taskId": task_id,
                            "sessionId": session_id,
                            "clientId": client_id,
                            "correlationId": correlationId,
                        },
                    )
                except Exception as sse_error:
                    logger.error(
                        "Failed to send SSE task delegation success event",
                        extra={
                            "error": str(sse_error),
                            "taskId": task_id,
                            "sessionId": session_id,
                            "correlationId": correlationId,
                        },
                        exc_info=True,
                    )

                logger.info(
                    "Task created successfully - background delegation complete",
                    extra={
                        "taskId": task_id,
                        "agentConversationId": agent_conversation_id,
                        "title": title,
                        "sessionId": session_id,
                        "correlationId": correlationId,
                    },
                )
            except Exception as e:
                logger.error(
                    "Failed to create task in background delegation",
                    extra={
                        "error": str(e),
                        "title": title,
                        "globalChatConversationId": globalChatConversationId,
                        "agentConversationId": agent_conversation_id,
                        "agentId": agentId,
                        "userId": userId,
                        "sessionId": session_id,
                        "correlationId": correlationId,
                    },
                    exc_info=True,
                )
                # Send SSE failure event
                self._send_task_delegation_failure_event(
                    error_code="TASK_CREATION_FAILED",
                    error_message=f"Failed to create task: {str(e)}",
                    correlation_id=correlationId,
                    global_session_id=globalSessionId,
                    user_id=userId,
                )
                return

            logger.info(
                "Background task delegation completed successfully",
                extra={
                    "taskId": task_id,
                    "agentConversationId": agent_conversation_id,
                    "sessionId": session_id,
                    "title": title,
                    "correlationId": correlationId,
                },
            )

        except Exception as e:
            logger.error(
                "Unexpected error during background task delegation",
                extra={
                    "error": str(e),
                    "agentId": agentId,
                    "globalChatConversationId": globalChatConversationId,
                    "userId": userId,
                    "title": title,
                    "correlationId": correlationId,
                },
                exc_info=True,
            )
            # Send SSE failure event for unexpected errors
            self._send_task_delegation_failure_event(
                error_code="UNEXPECTED_ERROR",
                error_message=f"Unexpected error during task delegation: {str(e)}",
                correlation_id=correlationId,
                global_session_id=globalSessionId,
                user_id=userId,
            )

    def _send_task_delegation_failure_event(
        self,
        error_code: str,
        error_message: str,
        correlation_id: Optional[str] = None,
        global_session_id: Optional[str] = None,
        user_id: Optional[str] = None,
    ):
        """Send SSE task delegation failure event."""
        try:
            client_id = global_session_id if global_session_id else user_id

            sse_manager.send_task_delegation_failed(
                error_code=error_code,
                error_message=error_message,
                correlation_id=correlation_id,
                global_session_id=global_session_id,
                client_id=client_id,
            )

            logger.info(
                "SSE task delegation failure event sent",
                extra={
                    "error_code": error_code,
                    "clientId": client_id,
                    "correlationId": correlation_id,
                },
            )
        except Exception as sse_error:
            logger.error(
                "Failed to send SSE task delegation failure event",
                extra={
                    "error": str(sse_error),
                    "error_code": error_code,
                    "correlationId": correlation_id,
                },
                exc_info=True,
            )
