import asyncio
from datetime import datetime
from typing import Dict, List, Optional

from app.core.config import settings
from app.core.logging import get_logger
from app.grpc_ import agent_pb2
from app.schemas.agent_details import AgentDetails
from app.services.agent_grpc_service import AgentServiceClient
from app.utils.redis.redis_service import RedisService

# Initialize logger
logger = get_logger(__name__)

# Cache configuration
AGENT_CACHE_TTL = getattr(settings, "AGENT_CACHE_TTL", 300)  # 5 minutes default
AGENT_CACHE_KEY_PREFIX = "agent_details:"


def _parse_timestamp(timestamp_field):
    """
    Parse timestamp field that can be either a protobuf Timestamp or a string.

    Args:
        timestamp_field: Either a protobuf Timestamp object or a string

    Returns:
        datetime or None: Parsed datetime object or None if empty/invalid
    """
    if not timestamp_field:
        return None

    try:
        # If it's a protobuf Timestamp object with ToDatetime method
        if hasattr(timestamp_field, "ToDatetime"):
            return timestamp_field.ToDatetime()
        # If it's a string, try to parse it as ISO format
        elif isinstance(timestamp_field, str):
            # Handle various ISO format strings
            if timestamp_field.endswith("Z"):
                return datetime.fromisoformat(timestamp_field.replace("Z", "+00:00"))
            else:
                return datetime.fromisoformat(timestamp_field)
        else:
            return None
    except Exception:
        # If parsing fails, return None instead of crashing
        return None


def convert_agent_proto_to_details(
    agent_proto: agent_pb2.Agent,
) -> AgentDetails:
    """
    Convert agent protobuf object to AgentDetails schema.

    Args:
        agent_proto: Agent protobuf object

    Returns:
        AgentDetails: Converted agent details
    """
    return AgentDetails(
        id=agent_proto.id,
        name=agent_proto.name,
        description=agent_proto.description,
        avatar=agent_proto.avatar,
        category=(
            agent_proto.category.name
            if hasattr(agent_proto.category, "name")
            else str(agent_proto.category)
        ),
        visibility=(
            agent_proto.visibility.name
            if hasattr(agent_proto.visibility, "name")
            else str(agent_proto.visibility)
        ),
        status=(
            agent_proto.status.name
            if hasattr(agent_proto.status, "name")
            else str(agent_proto.status)
        ),
        tags=list(agent_proto.tags) if agent_proto.tags else [],
        created_at=_parse_timestamp(agent_proto.created_at),
        updated_at=_parse_timestamp(agent_proto.updated_at),
        organization_id=agent_proto.organization_id,
    )


async def _get_cached_agent_details(
    redis_service: RedisService, agent_id: str
) -> Optional[AgentDetails]:
    """Get agent details from cache."""
    try:
        cache_key = f"{AGENT_CACHE_KEY_PREFIX}{agent_id}"
        cached_data = redis_service.get_data_from_redis(cache_key, "details")
        if cached_data:
            return AgentDetails(**cached_data)
    except Exception as e:
        logger.warning(f"Failed to get cached agent details for {agent_id}: {e}")
    return None


async def _cache_agent_details(
    redis_service: RedisService, agent_id: str, agent_details: AgentDetails
) -> None:
    """Cache agent details."""
    try:
        cache_key = f"{AGENT_CACHE_KEY_PREFIX}{agent_id}"
        redis_service.set_data_to_redis(
            cache_key, "details", agent_details.dict(), expiry=AGENT_CACHE_TTL
        )
    except Exception as e:
        logger.warning(f"Failed to cache agent details for {agent_id}: {e}")


async def fetch_agents_details(
    agent_ids: List[str], use_cache: bool = True
) -> Dict[str, AgentDetails]:
    """
    Fetch agent details for multiple agent IDs with caching support.

    Args:
        agent_ids: List of agent IDs to fetch
        use_cache: Whether to use Redis caching (default: True)

    Returns:
        Dict[str, AgentDetails]: Dictionary mapping agent ID to agent details
    """
    if not agent_ids:
        return {}

    # Remove duplicates and filter out None/empty values
    unique_agent_ids = list(set(filter(None, agent_ids)))
    if not unique_agent_ids:
        return {}

    agent_details_map = {}
    redis_service = RedisService() if use_cache else None
    uncached_agent_ids = []

    # Check cache first if enabled
    if use_cache and redis_service:
        cache_tasks = [
            _get_cached_agent_details(redis_service, agent_id) for agent_id in unique_agent_ids
        ]
        cached_results = await asyncio.gather(*cache_tasks, return_exceptions=True)

        for agent_id, cached_result in zip(unique_agent_ids, cached_results):
            if isinstance(cached_result, AgentDetails):
                agent_details_map[agent_id] = cached_result
                logger.debug(f"Cache hit for agent {agent_id}")
            else:
                uncached_agent_ids.append(agent_id)
                if not isinstance(cached_result, Exception):
                    logger.debug(f"Cache miss for agent {agent_id}")
    else:
        uncached_agent_ids = unique_agent_ids

    # Fetch uncached agents from gRPC service
    if uncached_agent_ids:
        try:
            agent_service = AgentServiceClient()
            response = await agent_service.getAgentsByIds(uncached_agent_ids)

            if response.success and response.agents:
                # Process fetched agents
                cache_tasks = []
                for agent_proto in response.agents:
                    agent_details = convert_agent_proto_to_details(agent_proto)
                    agent_details_map[agent_proto.id] = agent_details

                    # Cache the result if caching is enabled
                    if use_cache and redis_service:
                        cache_tasks.append(
                            _cache_agent_details(redis_service, agent_proto.id, agent_details)
                        )

                # Execute caching tasks in parallel
                if cache_tasks:
                    await asyncio.gather(*cache_tasks, return_exceptions=True)

                logger.info(f"Fetched {len(response.agents)} agent details from gRPC service")

        except Exception as e:
            logger.error(f"Failed to fetch agent details: {str(e)}")

    logger.info(
        f"Returned {len(agent_details_map)} agent details "
        f"({len(agent_details_map) - len(uncached_agent_ids)} from cache, "
        f"{len(uncached_agent_ids)} from service)"
    )

    return agent_details_map


async def enrich_conversations_with_agent_details(
    conversations: List[Dict], use_cache: bool = True
) -> List[Dict]:
    """
    Enrich conversation data with agent details.
    Also enriches tasks within conversations with their respective agent
    details.

    Args:
        conversations: List of conversation dictionaries
        use_cache: Whether to use Redis caching for agent details

    Returns:
        List[Dict]: Conversations enriched with agent details, including tasks
    """
    # Extract unique agent IDs from conversations and their tasks
    agent_ids = []
    for conv in conversations:
        # Add conversation agent ID
        if conv.get("agentId"):
            agent_ids.append(conv["agentId"])

        # Add agent IDs from tasks within the conversation
        if conv.get("tasks"):
            for task in conv["tasks"]:
                if task.get("agentId"):
                    agent_ids.append(task["agentId"])

    # Fetch agent details with caching
    agent_details_map = await fetch_agents_details(agent_ids, use_cache=use_cache)

    # Enrich conversations with agent details
    enriched_conversations = []
    for conv in conversations:
        enriched_conv = conv.copy()

        # Enrich conversation agent details
        agent_id = conv.get("agentId")
        if agent_id and agent_id in agent_details_map:
            enriched_conv["agentDetails"] = agent_details_map[agent_id].dict()

        # Enrich tasks within the conversation with agent details
        if enriched_conv.get("tasks"):
            enriched_tasks = []
            for task in enriched_conv["tasks"]:
                enriched_task = task.copy()
                task_agent_id = task.get("agentId")
                if task_agent_id and task_agent_id in agent_details_map:
                    enriched_task["agentDetails"] = agent_details_map[task_agent_id].dict()
                enriched_tasks.append(enriched_task)
            enriched_conv["tasks"] = enriched_tasks

        enriched_conversations.append(enriched_conv)

    return enriched_conversations


async def enrich_tasks_with_agent_details(tasks: List[Dict], use_cache: bool = True) -> List[Dict]:
    """
    Enrich task data with agent details.

    Args:
        tasks: List of task dictionaries
        use_cache: Whether to use Redis caching for agent details

    Returns:
        List[Dict]: Tasks enriched with agent details
    """
    # Extract unique agent IDs from tasks
    agent_ids = []
    for task in tasks:
        if task.get("agentId"):
            agent_ids.append(task["agentId"])

    # Fetch agent details with caching
    agent_details_map = await fetch_agents_details(agent_ids, use_cache=use_cache)

    # Enrich tasks with agent details
    enriched_tasks = []
    for task in tasks:
        enriched_task = task.copy()
        agent_id = task.get("agentId")
        if agent_id and agent_id in agent_details_map:
            enriched_task["agentDetails"] = agent_details_map[agent_id].dict()
        enriched_tasks.append(enriched_task)

    return enriched_tasks
