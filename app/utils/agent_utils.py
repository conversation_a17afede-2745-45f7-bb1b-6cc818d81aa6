from datetime import datetime
from typing import Dict, List

from app.grpc_ import agent_pb2
from app.schemas.agent_details import AgentDetails
from app.services.agent_grpc_service import AgentServiceClient


def _parse_timestamp(timestamp_field):
    """
    Parse timestamp field that can be either a protobuf Timestamp or a string.

    Args:
        timestamp_field: Either a protobuf Timestamp object or a string

    Returns:
        datetime or None: Parsed datetime object or None if empty/invalid
    """
    if not timestamp_field:
        return None

    try:
        # If it's a protobuf Timestamp object with ToDatetime method
        if hasattr(timestamp_field, "ToDatetime"):
            return timestamp_field.ToDatetime()
        # If it's a string, try to parse it as ISO format
        elif isinstance(timestamp_field, str):
            # Handle various ISO format strings
            if timestamp_field.endswith("Z"):
                return datetime.fromisoformat(timestamp_field.replace("Z", "+00:00"))
            else:
                return datetime.fromisoformat(timestamp_field)
        else:
            return None
    except Exception:
        # If parsing fails, return None instead of crashing
        return None


def convert_agent_proto_to_details(
    agent_proto: agent_pb2.Agent,
) -> AgentDetails:
    """
    Convert agent protobuf object to AgentDetails schema.

    Args:
        agent_proto: Agent protobuf object

    Returns:
        AgentDetails: Converted agent details
    """
    return AgentDetails(
        id=agent_proto.id,
        name=agent_proto.name,
        description=agent_proto.description,
        avatar=agent_proto.avatar,
        category=(
            agent_proto.category.name
            if hasattr(agent_proto.category, "name")
            else str(agent_proto.category)
        ),
        visibility=(
            agent_proto.visibility.name
            if hasattr(agent_proto.visibility, "name")
            else str(agent_proto.visibility)
        ),
        status=(
            agent_proto.status.name
            if hasattr(agent_proto.status, "name")
            else str(agent_proto.status)
        ),
        tags=list(agent_proto.tags) if agent_proto.tags else [],
        created_at=_parse_timestamp(agent_proto.created_at),
        updated_at=_parse_timestamp(agent_proto.updated_at),
        organization_id=agent_proto.organization_id,
    )


async def fetch_agents_details(
    agent_ids: List[str],
) -> Dict[str, AgentDetails]:
    """
    Fetch agent details for multiple agent IDs.

    Args:
        agent_ids: List of agent IDs to fetch

    Returns:
        Dict[str, AgentDetails]: Dictionary mapping agent ID to agent details
    """
    if not agent_ids:
        return {}

    agent_service = AgentServiceClient()
    agent_details_map = {}

    try:
        # Remove duplicates and filter out None/empty values
        unique_agent_ids = list(set(filter(None, agent_ids)))

        if not unique_agent_ids:
            return {}

        # Fetch agents using the gRPC service
        response = await agent_service.getAgentsByIds(unique_agent_ids)

        if response.success and response.agents:
            for agent_proto in response.agents:
                agent_details = convert_agent_proto_to_details(agent_proto)
                agent_details_map[agent_proto.id] = agent_details

    except Exception as e:
        # Log error but don't fail the API call
        print(f"[WARNING] Failed to fetch agent details: {str(e)}")

    return agent_details_map


async def enrich_conversations_with_agent_details(
    conversations: List[Dict],
) -> List[Dict]:
    """
    Enrich conversation data with agent details.
    Also enriches tasks within conversations with their respective agent
    details.

    Args:
        conversations: List of conversation dictionaries

    Returns:
        List[Dict]: Conversations enriched with agent details, including tasks
    """
    # Extract unique agent IDs from conversations and their tasks
    agent_ids = []
    for conv in conversations:
        # Add conversation agent ID
        if conv.get("agentId"):
            agent_ids.append(conv["agentId"])

        # Add agent IDs from tasks within the conversation
        if conv.get("tasks"):
            for task in conv["tasks"]:
                if task.get("agentId"):
                    agent_ids.append(task["agentId"])

    # Fetch agent details
    agent_details_map = await fetch_agents_details(agent_ids)

    # Enrich conversations with agent details
    enriched_conversations = []
    for conv in conversations:
        enriched_conv = conv.copy()

        # Enrich conversation agent details
        agent_id = conv.get("agentId")
        if agent_id and agent_id in agent_details_map:
            enriched_conv["agentDetails"] = agent_details_map[agent_id].dict()

        # Enrich tasks within the conversation with agent details
        if enriched_conv.get("tasks"):
            enriched_tasks = []
            for task in enriched_conv["tasks"]:
                enriched_task = task.copy()
                task_agent_id = task.get("agentId")
                if task_agent_id and task_agent_id in agent_details_map:
                    enriched_task["agentDetails"] = agent_details_map[task_agent_id].dict()
                enriched_tasks.append(enriched_task)
            enriched_conv["tasks"] = enriched_tasks

        enriched_conversations.append(enriched_conv)

    return enriched_conversations


async def enrich_tasks_with_agent_details(tasks: List[Dict]) -> List[Dict]:
    """
    Enrich task data with agent details.

    Args:
        tasks: List of task dictionaries

    Returns:
        List[Dict]: Tasks enriched with agent details
    """
    # Extract unique agent IDs from tasks
    agent_ids = []
    for task in tasks:
        if task.get("agentId"):
            agent_ids.append(task["agentId"])

    # Fetch agent details
    agent_details_map = await fetch_agents_details(agent_ids)

    # Enrich tasks with agent details
    enriched_tasks = []
    for task in tasks:
        enriched_task = task.copy()
        agent_id = task.get("agentId")
        if agent_id and agent_id in agent_details_map:
            enriched_task["agentDetails"] = agent_details_map[agent_id].dict()
        enriched_tasks.append(enriched_task)

    return enriched_tasks
